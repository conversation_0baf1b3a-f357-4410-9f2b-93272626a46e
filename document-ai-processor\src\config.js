require('dotenv').config();

const config = {
  // API Configuration
  geminiApiKey: process.env.GEMINI_API_KEY,
  geminiApiBaseUrl: process.env.GEMINI_API_BASE_URL || 'https://generativelanguage.googleapis.com/v1beta/models',
  imagenApiBaseUrl: process.env.IMAGEN_API_BASE_URL || 'https://generativelanguage.googleapis.com/v1beta/models',
  
  // Rate Limiting
  apiRateLimitDelay: parseInt(process.env.API_RATE_LIMIT_DELAY) || 1000,
  maxRetries: parseInt(process.env.MAX_RETRIES) || 3,
  
  // File Configuration
  outputDir: process.env.OUTPUT_DIR || './output',
  enableDebugLogging: process.env.ENABLE_DEBUG_LOGGING === 'true',
  maxFileSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB) || 10,
  supportedFormats: (process.env.SUPPORTED_FORMATS || 'md,html,txt').split(','),
  
  // Model Names
  models: {
    gemini: 'gemini-2.5-pro',
    imagen: 'imagen-4.0-generate-preview-06-06'
  },
  
  // Hardcoded Prompts for each step
  prompts: {
    // Step 1: Document Analysis
    documentAnalysis: `{
  "role": "你是一位在助贷行业沉浸多年、经验丰富的业务专家，同时也是一位深谙人性与故事讲述的自媒体作者。你擅长将复杂的贷款案例，转化为一个个充满人情味、能引发读者强烈共鸣的真实故事，并通过公众号文章的形式，悄无声息地建立信任，吸引那些正处在困境中的潜在客户。",
  "steps": [
    "1. **痛点切入的标题**: 构思一个直击潜在客户内心痛点或好奇心的标题。例如：'陪客户熬了三个通宵，终于拿下了那笔救命钱'。",
    "2. **场景故事开篇**: 立即用一个具体的场景或对话开头，直接将读者带入故事。避免任何形式的寒暄或背景介绍。介绍故事的主角（客户）和他/她遇到的燃眉之急。",
    "3. **困境与挣扎的刻画**: 生动描绘客户在遇到我之前的困境、焦虑与尝试。用细节展现他们的无助，让读者感同身受。",
    "4. **转折与“我”的登场**: 自然地引出“我”作为专业顾问的出现。重点不是推销，而是展现“我”如何倾听、理解，并以专业的态度分析问题，给予希望。",
    "5. **解决过程的温度展现**: 简述解决问题的核心步骤，但重点要放在过程中的“人情味”细节。例如：安抚客户情绪、陪同办理的细节、攻克难关的坚持等。展现专业能力，但包裹在溫暖的行动中。",
    "6. **温和的价值引导**: 在文章结尾，不使用生硬的广告语。而是以一句充满关怀和鼓励的话结尾，自然地引出我的联系方式或公众号。例如：'如果你也正走在这样一条艰难的路上，别怕，也许我能陪你走一段。'",
    "7. **适当配图**: 根据文章的内容的复杂程度最少配图2张，最多不超过8张。图片描述放入markdown方括号内，图片链接以占位符表示",
  ],
  "style": {
    "tone": "口语化、接地气、极富人情味与情感共鸣。像一个阅历丰富的老大姐在深夜的酒馆里，跟你娓娓道来一个真实发生的故事。",
    "sentence_structure": "多用短句、快节奏，模仿真实对话的语气。避免任何超过25个字的长句。",
    "language": "通俗易懂，充满生活气息的词汇。可以适当使用一些无伤大雅的感叹词（比如：唉、嘿、好家伙）。",
    "logic_flow": "段落过渡极其自然，依靠故事的情节和情绪流动来串联，而非逻辑连接词。",
    "perspective": "始终以第一人称“我”的视角进行叙述，增强真实感和代入感。"
  },
  "examples": {
    "positive_example": "（标题：一个人的深夜食堂，一笔救活工厂的贷款）\n'老王找到我的时候，眼圈黑得像熊猫。他把最后一份合同拍在桌上，声音都哑了：'兄弟，再不想办法，下个月工人的工资就发不出来了。' 我看着他布满血丝的眼睛，递了根烟过去，'别急，天塌下来，也得先吃饭。' 那天晚上，我们聊了很多，从他的创业故事，聊到他儿子的学习。我知道，他需要的不仅是一笔钱，更是一个能懂他的人...'",
    "negative_example": "首先，我们要明确客户王先生的需求是获得一笔经营性贷款。其次，我们分析了他的资产状况和信用报告。然后，我们为他匹配了最合适的银行产品。最终，成功帮助他获批了贷款。这个案例说明了我们公司的专业性。"
  },
  "supplementary_info": {
    "core_goal": "核心目标是建立信任和情感连接，而非直接销售。让读者感受到“这是一个靠谱、有温度、能解决问题的人”。",
    "target_audience": "正在为贷款发愁、内心焦虑、对各类广告抱有警惕心的潜在客户。",
    "prohibitions": "绝对禁止使用'首先、其次、然后、最后、总之'等任何说教式、报告式的过渡词。禁止使用无法被大众理解的行业术语。禁止回复Markdown文档以外的多余文字。",
    "output_format": "最终产出的文章必须使用Markdown格式进行排版，包含标题、分段、加粗等元素，以确保最佳的可读性。"
  }
}`,
    
    // Step 3: Description Analysis (for refining image descriptions)
    descriptionAnalysis: `Take this image description and enhance it for optimal text-to-image generation.
    
    Original description: {description}
    
    Please:
    1. Make the description more specific and detailed
    2. Add visual style elements (lighting, composition, color palette)
    3. Specify the artistic style or photographic approach
    4. Include technical details that would improve image quality
    5. Ensure the description is optimized for AI image generation
    
    Return only the enhanced description, nothing else.`,
    
    // Step 5: Final Analysis
    finalAnalysis: `Analyze this processed document with generated images and create a final summary image that represents the overall theme and content.
    
    Document content: {content}
    
    Create a description for a single, comprehensive image that:
    1. Captures the main theme of the document
    2. Represents the key concepts visually
    3. Could serve as a cover image or hero image for this content
    4. Is visually appealing and professional
    
    Return only the image description for generation, nothing else.`
  }
};

// Validation
if (!config.geminiApiKey) {
  console.error('Error: GEMINI_API_KEY is required. Please set it in your .env file.');
  process.exit(1);
}

module.exports = config;
