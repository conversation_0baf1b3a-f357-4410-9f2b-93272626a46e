# Logs
logs
*.log
certs/*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
/upload_images

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the next line if you're using Gatsby Cloud
# .gatsby/
public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# macOS files
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

# Temporary files created by editors
*~
#*.swp

# IDE config folders
.idea/
.vscode/

# Custom
errors/

# Python
__pycache__/
*.py[cod]
*$py.class

# Python Libraries
*.egg-info/
*.egg

# Distribution / packaging
.Python
build/
dist/
part/
sdist/
*.manifest
*.spec
wheels/

# PyInstaller
# Usually these files are written by a python script from a template
# before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints
profile_default/
ipython_config.py

# pyenv
.python-version

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Error snapshots directory (Python specific)
errors_py/
logs/

# Authentication Profiles (Sensitive)
auth_profiles/active/*
!auth_profiles/active/.gitkeep
auth_profiles/saved/*
!auth_profiles/saved/.gitkeep

# Camoufox/Playwright Profile Data (Assume these are generated/temporary)
camoufox_profile/
chrome_temp_profile/

# Deprecated Javascript Version node_modules
deprecated_javascript_version/node_modules/ 

.roomodes
memory-bank/
gui_config.json

# key
key.txt

# 脚本注入相关文件
# 用户自定义的模型配置文件（保留示例文件）
browser_utils/model_configs.json
browser_utils/my_*.json
# 用户自定义的油猴脚本（如果不是默认的）
browser_utils/custom_*.js
browser_utils/my_*.js
# 临时生成的脚本文件
browser_utils/generated_*.js
# Docker 环境的实际配置文件（保留示例文件）
docker/.env
docker/my_*.json