{"include": ["."], "exclude": ["**/__pycache__", "**/*.pyc", ".git", ".venv", "node_modules", "deprecated_javascript_version", "errors_py", "logs"], "extraPaths": [".", "./api_utils", "./browser_utils", "./config", "./models", "./logging_utils", "./stream"], "pythonVersion": "3.13", "pythonPlatform": "<PERSON>", "typeCheckingMode": "off", "useLibraryCodeForTypes": true, "autoImportCompletions": true, "autoSearchPaths": true, "stubPath": "", "reportMissingImports": "none", "reportMissingTypeStubs": "none", "reportUnusedImport": "none", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedVariable": "none", "reportDuplicateImport": "none", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportGeneralTypeIssues": "none", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportUntypedBaseClass": "none", "reportUntypedNamedTuple": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "none", "reportIncompatibleVariableOverride": "none", "reportInconsistentConstructor": "none"}