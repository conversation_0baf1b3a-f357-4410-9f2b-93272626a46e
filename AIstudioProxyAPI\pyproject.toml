[tool.poetry]
name = "aistudioproxyapi"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.9,<4.0"
fastapi = "==0.115.12"
pydantic = ">=2.7.1,<3.0.0"
uvicorn = "==0.29.0"
python-dotenv = "==1.0.1"
websockets = "==12.0"
httptools = "==0.6.1"
uvloop = {version = "*", markers = "sys_platform != 'win32'"}
playwright = "*"
camoufox = {version = "0.4.11", extras = ["geoip"]}
cryptography = "==42.0.5"
aiohttp = "~=3.9.5"
requests = "==2.31.0"
pyjwt = "==2.8.0"
Flask = "==3.0.3"
aiosocks = "~=0.2.6"
python-socks = "~=2.7.1"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
black = "^23.0.0"
isort = "^5.12.0"
mypy = "^1.0.0"
flake8 = "^6.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
