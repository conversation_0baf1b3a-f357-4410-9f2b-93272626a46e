# Web UI 使用指南

本项目内置了一个功能丰富的现代化 Web 用户界面，提供聊天测试、状态监控、API密钥管理等完整功能。

## 访问方式

在浏览器中打开服务器的根地址，默认为 `http://127.0.0.1:2048/`。

**端口配置**：
- 默认端口：2048
- 配置方式：在 `.env` 文件中设置 `PORT=2048` 或 `DEFAULT_FASTAPI_PORT=2048`
- 命令行覆盖：使用 `--server-port` 参数
- GUI配置：通过图形启动器直接设置

## 主要功能

### 聊天界面

- **基本聊天**: 发送消息并接收来自 AI Studio 的回复，支持三层响应获取机制
- **Markdown 支持**: 支持 Markdown 格式化、代码块高亮和数学公式渲染
- **自动API密钥认证**: 对话请求会自动包含Bearer token认证，支持本地存储
- **智能错误处理**: 针对401认证错误、配额超限等提供专门的中文提示信息
- **输入验证**: 防止发送空消息，双重检查确保内容有效性
- **流式响应**: 支持实时流式输出，提供类似ChatGPT的打字机效果
- **客户端断开检测**: 智能检测客户端连接状态，优化资源使用

### 服务器信息

切换到 "服务器信息" 标签页可以查看：

- **API 调用信息**: Base URL、模型名称、认证状态等
- **服务健康检查**: `/health` 端点的详细状态，包括：
  - Playwright 连接状态
  - 浏览器连接状态
  - 页面就绪状态
  - 队列工作器状态
  - 当前队列长度
- **系统状态**: 三层响应获取机制的状态
- **实时更新**: 提供 "刷新" 按钮手动更新信息

### 安全的API密钥管理系统

"设置" 标签页提供完整的密钥管理功能：

#### 分级权限查看系统

**工作原理**:
- **未验证状态**: 只显示基本的密钥输入界面和提示信息
- **验证成功后**: 显示完整的密钥管理界面，包括服务器密钥列表

**验证流程**:
1. 在密钥输入框中输入有效的API密钥
2. 点击"验证密钥"按钮进行验证
3. 验证成功后，界面自动刷新显示完整功能
4. 验证状态在浏览器会话期间保持有效

#### 密钥管理功能

**密钥验证**:
- 支持验证任意API密钥的有效性
- 验证成功的密钥会自动保存到浏览器本地存储
- 验证失败会显示具体的错误信息

**密钥列表查看**:
- 显示服务器上配置的所有API密钥
- 所有密钥都经过打码处理显示（格式：`xxxx****xxxx`）
- 显示密钥的添加时间和状态信息
- 提供单独的密钥验证按钮

**安全机制**:
- **打码显示**: 所有密钥都经过安全打码处理，保护敏感信息
- **会话保持**: 验证状态仅在当前浏览器会话中有效
- **本地存储**: 验证成功的密钥保存在浏览器本地存储中
- **重置功能**: 可随时重置验证状态，重新进行密钥验证

#### 密钥输入界面

**自动保存**: 输入框内容会自动保存到浏览器本地存储
**快捷操作**: 支持回车键快速验证
**可见性切换**: 提供密钥可见性切换按钮
**状态指示**: 实时显示当前的验证状态和密钥配置情况

### 模型设置

"模型设置" 标签页允许用户配置并保存（至浏览器本地存储）以下参数：

- **系统提示词 (System Prompt)**: 自定义指导模型的行为和角色
- **温度 (Temperature)**: 控制生成文本的随机性
- **最大输出Token (Max Output Tokens)**: 限制模型单次回复的长度
- **Top-P**: 控制核心采样的概率阈值
- **停止序列 (Stop Sequences)**: 指定一个或多个序列，当模型生成这些序列时将停止输出
- 提供"保存设置"和"重置为默认值"按钮

### 模型选择器

在主聊天界面可以选择希望使用的模型，选择后会尝试在 AI Studio 后端进行切换。

### 系统日志

右侧有一个可展开/收起的侧边栏，通过 WebSocket (`/ws/logs`) 实时显示后端日志：

- 包含日志级别、时间戳和消息内容
- 提供清理日志的按钮
- 用于调试和监控

### 主题切换

右上角提供 "浅色"/"深色" 按钮，用于切换界面主题，偏好设置会保存在浏览器本地存储中。

### 响应式设计

界面会根据屏幕大小自动调整布局。

## 使用说明

### 首次使用

1. 启动服务后，在浏览器中访问 `http://127.0.0.1:2048/`

2. **API密钥配置检查**：
   - 访问"设置"标签页查看API密钥状态
   - 如果显示"不需要API密钥"，则可以直接使用
   - 如果显示"需要API密钥"，则需要进行密钥验证

3. **API密钥验证流程**（如果需要）：
   - 在"API密钥管理"区域输入有效的API密钥
   - 点击"验证密钥"按钮进行验证
   - 验证成功后界面会自动刷新，显示：
     - 验证成功的状态指示
     - 服务器上配置的密钥列表（打码显示）
     - 完整的密钥管理功能

4. **密钥获取方式**：
   - 如果是管理员：可以直接查看项目根目录下的 `key.txt` 文件
   - 如果是用户：需要联系管理员获取有效的API密钥
   - 密钥格式：至少8个字符的字符串

### 日常使用

3. 在聊天界面输入消息进行对话测试（会自动使用验证过的密钥进行认证）
4. 通过"服务器信息"标签查看服务状态
5. 在"模型设置"标签中调整对话参数
6. 侧边栏显示实时系统日志，可用于调试和监控

## 安全机制说明

- **分级权限**: 未验证状态下只显示基本信息，验证成功后显示完整的密钥管理界面
- **会话保持**: 验证状态在浏览器会话期间保持，无需重复验证
- **安全显示**: 所有密钥都经过打码处理，保护敏感信息
- **重置功能**: 可随时重置验证状态，重新进行密钥验证
- **自动认证**: 对话请求自动包含认证头，确保API调用安全

## 用途

这个 Web UI 主要用于：

- 简单聊天测试
- 开发调试
- 快速验证代理是否正常工作
- 监控服务器状态
- 安全管理API密钥
- 方便地调整和测试模型参数

## 故障排除

### 无法显示日志或服务器信息

- 检查浏览器开发者工具 (F12) 的控制台和网络选项卡是否有错误
- 确认 WebSocket 连接 (`/ws/logs`) 是否成功建立
- 确认 `/health` 和 `/api/info` 端点是否能正常访问并返回数据

### API密钥管理问题

- **无法验证密钥**: 检查输入的密钥格式，确认服务器上的 `key.txt` 文件包含有效密钥
- **验证成功但无法查看密钥列表**: 检查浏览器控制台是否有JavaScript错误，尝试刷新页面
- **验证状态丢失**: 验证状态仅在当前浏览器会话中有效，关闭浏览器或标签页会丢失状态
- **密钥显示异常**: 确认 `/api/keys` 端点返回正确的JSON格式数据

### 对话功能问题

- **发送消息后收到401错误**: API密钥认证失败，需要在设置页面重新验证密钥
- **无法发送空消息**: 这是正常的安全机制，确保输入有效内容后再发送
- **对话请求失败**: 检查网络连接，确认服务器正常运行，查看浏览器控制台和服务器日志

## 下一步

Web UI 使用完成后，请参考：
- [API 使用指南](api-usage.md)
- [故障排除指南](troubleshooting.md)
