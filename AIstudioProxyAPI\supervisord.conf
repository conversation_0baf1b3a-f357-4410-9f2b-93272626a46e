# /etc/supervisor/conf.d/app.conf
[supervisord]
nodaemon=true
logfile=/dev/null
logfile_maxbytes=0
pidfile=/tmp/supervisord.pid

[program:aistudioproxy]
command=python launch_camoufox.py --headless --server-port %(ENV_SERVER_PORT)s --stream-port %(ENV_STREAM_PORT)s --internal-camoufox-proxy "%(ENV_INTERNAL_CAMOUFOX_PROXY)s" --helper ''
directory=/app
autostart=true
autorestart=true
killasgroup=true
stopasgroup=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
user=appuser
environment=PYTHONUNBUFFERED="1",HOME="/app",PLAYWRIGHT_BROWSERS_PATH="/home/<USER>/.cache/ms-playwright"