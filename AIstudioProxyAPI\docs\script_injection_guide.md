# 油猴脚本动态挂载功能使用指南

## 概述

本功能允许您动态挂载油猴脚本来增强 AI Studio 的模型列表，支持自定义模型注入和配置管理。脚本更新后只需重启服务即可生效，无需手动修改代码。

**⚠️ 重要更新 (v3.0)**:
- **革命性改进** - 使用 Playwright 原生网络拦截，彻底解决时序和可靠性问题
- **双重保障** - Playwright 路由拦截 + JavaScript 脚本注入，确保万无一失
- **完全改变工作机制** - 现在直接从油猴脚本解析模型列表
- **移除配置文件依赖** - 不再需要手动维护模型配置文件
- **自动同步** - 前端和后端使用相同的模型数据源
- **无适配负担** - 油猴脚本更新时无需手动更新配置

## 功能特性

- ✅ **Playwright 原生拦截** - 使用 Playwright 路由拦截，100% 可靠
- ✅ **双重保障机制** - 网络拦截 + 脚本注入，确保万无一失
- ✅ **直接脚本解析** - 从油猴脚本中自动解析模型列表
- ✅ **前后端同步** - 前端和后端使用相同的模型数据源
- ✅ **零配置维护** - 无需手动维护模型配置文件
- ✅ **自动适配** - 脚本更新时自动获取新的模型列表
- ✅ **灵活配置** - 支持环境变量配置
- ✅ **静默失败** - 脚本文件不存在时静默跳过，不影响主要功能

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 是否启用脚本注入功能
ENABLE_SCRIPT_INJECTION=true

# 油猴脚本文件路径（相对于项目根目录）
# 模型数据直接从此脚本文件中解析，无需额外配置文件
USERSCRIPT_PATH=browser_utils/more_modles.js
```

### 工作原理说明

**新的工作机制 (v2.0)**:

```
油猴脚本 → 前端直接注入 + 后端解析模型列表 → API同步
```

1. **前端**: 直接注入原始油猴脚本到浏览器页面
2. **后端**: 解析脚本中的 `MODELS_TO_INJECT` 数组
3. **同步**: 将解析出的模型添加到API模型列表

**优势**:
- ✅ **单一数据源** - 模型数据直接从油猴脚本解析，无需配置文件
- ✅ **自动同步** - 脚本更新时自动获取新模型，保持前后端一致
- ✅ **完美适配** - 与油猴脚本显示效果100%一致（emoji、版本号等）
- ✅ **零维护成本** - 无需为脚本更新做任何适配工作

## 使用方法

### 1. 启用脚本注入

确保在 `.env` 文件中设置：
```bash
ENABLE_SCRIPT_INJECTION=true
```

### 2. 准备脚本文件 (必需)

将您的油猴脚本放在 `browser_utils/more_modles.js`（或您在 `USERSCRIPT_PATH` 中指定的路径）。

**⚠️ 脚本文件必须存在，否则不会执行任何注入操作。**

### 3. 启动服务

正常启动 AI Studio Proxy 服务，系统将：

1. **前端注入** - 直接将油猴脚本注入到浏览器页面
2. **后端解析** - 自动解析脚本中的模型列表
3. **API同步** - 将解析出的模型添加到API响应中

**就这么简单！** 无需任何配置文件维护。

### 4. 验证注入效果

- **前端**: 在AI Studio页面上可以看到注入的模型
- **API**: 通过 `/v1/models` 端点可以获取包含注入模型的完整列表

**注意**: 如果脚本文件不存在，系统会静默跳过注入操作，不会显示错误信息。

### 🚀 革命性改进 (v3.0)

**问题**: JavaScript 脚本拦截存在时序问题和浏览器安全限制，可能无法可靠地拦截网络请求。

**解决方案**: 使用 Playwright 原生的网络拦截功能 (`context.route()`)，在网络层面直接拦截和修改响应，彻底解决可靠性问题。

**技术细节**:
- 使用 `context.route("**/*", handler)` 拦截所有请求
- 在网络层面识别和修改模型列表响应
- 不依赖浏览器 JavaScript 环境
- 同时保留脚本注入作为备用方案

**核心优势**:
- 🎯 **100% 可靠** - 不受浏览器安全策略影响
- ⚡ **更早拦截** - 在网络层面拦截，比 JavaScript 更早
- 🛡️ **双重保障** - 网络拦截 + 脚本注入，确保万无一失

## 工作原理

### 🔄 双重拦截机制

1. **启用检查** - 检查 `ENABLE_SCRIPT_INJECTION` 环境变量
2. **脚本存在性验证** - 检查油猴脚本文件是否存在
3. **Playwright 路由拦截** - 使用 `context.route()` 拦截所有网络请求
4. **模型列表请求识别** - 检测包含 `alkalimakersuite` 和 `ListModels` 的请求
5. **响应修改** - 直接修改模型列表响应，注入自定义模型
6. **脚本注入备用** - 同时注入 JavaScript 脚本作为备用方案
7. **后端解析** - 使用JSON解析技术解析脚本中的 `MODELS_TO_INJECT` 数组
8. **API集成** - 将解析出的模型（保留原始emoji和版本信息）添加到后端模型列表

### 🎯 技术优势

- ✅ **Playwright 原生拦截** - 不受浏览器安全限制，100% 可靠
- ✅ **更早的拦截时机** - 在网络层面拦截，比 JavaScript 更早
- ✅ **双重保障** - 网络拦截失败时，JavaScript 脚本作为备用
- ✅ **单一数据源** - 油猴脚本是唯一的模型定义源
- ✅ **自动同步** - 前端和后端自动保持一致
- ✅ **零维护** - 脚本更新时无需任何手动操作
- ✅ **向后兼容** - 支持现有的油猴脚本格式

## 重要说明 ⚠️

### 前端和后端双重注入

本功能实现了**前端和后端的双重模型注入**：

1. **前端注入** - 油猴脚本在浏览器页面上显示注入的模型
2. **后端注入** - API服务器的模型列表也包含注入的模型

这确保了：
- ✅ 在AI Studio页面上可以看到注入的模型
- ✅ 通过API调用时可以使用注入的模型
- ✅ 前端显示和后端API保持一致

### 模型调用说明

注入的模型可以正常通过API调用，例如：

```bash
curl -X POST http://localhost:2048/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "kingfall-ab-test",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

## 日志输出

启用脚本注入后，您将在日志中看到类似输出：

```
# 网络拦截相关日志
设置网络拦截和脚本注入...
成功设置模型列表网络拦截
成功解析 6 个模型从油猴脚本

# 模型列表响应处理时的日志
捕获到潜在的模型列表响应来自: https://alkalimakersuite.googleapis.com/...
添加了 6 个注入的模型到API模型列表
成功解析和更新模型列表。总共解析模型数: 12

# 解析出的模型示例
👑 Kingfall (Script v1.6)
✨ Gemini 2.5 Pro 03-25 (Script v1.6)
🦁 Goldmane (Script v1.6)
```

## 故障排除

### 脚本注入失败

1. **检查文件路径** - 确保 `USERSCRIPT_PATH` 指向的文件存在
2. **检查文件权限** - 确保脚本文件可读
3. **查看日志** - 检查详细的错误信息

### 模型解析失败

1. **脚本格式** - 确保油猴脚本中的 `MODELS_TO_INJECT` 数组格式正确
2. **必需字段** - 确保每个模型都有 `name` 和 `displayName` 字段
3. **JavaScript语法** - 确保脚本文件是有效的JavaScript格式

### 禁用脚本注入

如果遇到问题，可以临时禁用脚本注入：

```bash
ENABLE_SCRIPT_INJECTION=false
```

## 高级用法

### 自定义脚本路径

您可以使用不同的脚本文件：

```bash
USERSCRIPT_PATH=custom_scripts/my_script.js
```

### 多套脚本

通过修改 `USERSCRIPT_PATH` 可以使用不同的油猴脚本：

```bash
USERSCRIPT_PATH=custom_scripts/production_models.js
```

### 版本管理

系统会自动解析脚本中的版本信息，保持与油猴脚本完全一致的显示效果，包括emoji和版本标识。

## 注意事项

1. **重启生效** - 脚本文件更新后需要重启服务
2. **浏览器缓存** - 如果模型列表没有更新，尝试刷新页面或清除浏览器缓存
3. **兼容性** - 确保您的油猴脚本与当前的 AI Studio 页面结构兼容
4. **性能影响** - 大量模型注入可能影响页面加载性能
5. **显示一致性** - 系统确保前端显示与油猴脚本效果100%一致

## 示例配置

完整的 `.env` 配置示例：

```bash
# 基础配置
PORT=2048
ENABLE_SCRIPT_INJECTION=true

# 脚本配置
USERSCRIPT_PATH=browser_utils/more_modles.js

# 其他配置...
```

## 技术细节

- **脚本管理器类** - `ScriptManager` 负责所有脚本相关操作
- **配置集成** - 与现有的配置系统无缝集成
- **错误恢复** - 脚本注入失败不会影响主要功能
- **日志记录** - 详细的操作日志便于调试
